import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { FiUsers, FiUser, FiMail, FiPhone, FiMapPin, FiCalendar, FiShoppingBag, FiDollarSign, FiArrowLeft, FiSearch, FiFilter, FiEye, FiEdit, FiUserPlus, FiDownload, FiStar, FiGift, FiPlus, FiMinus, FiSave, FiX, FiClock } from 'react-icons/fi';
import AdminLayout from '../../components/admin/AdminLayout';

const CustomersManagementContent = () => {
  const router = useRouter();
  const { store } = router.query;
  const [customers, setCustomers] = useState([]);
  const [loyaltyData, setLoyaltyData] = useState({});
  const [loading, setLoading] = useState(true);
  const [loyaltyLoading, setLoyaltyLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('all');
  const [selectedCustomer, setSelectedCustomer] = useState(null);
  const [showCustomerDetail, setShowCustomerDetail] = useState(false);
  const [showAddCustomer, setShowAddCustomer] = useState(false);
  const [showLoyaltyModal, setShowLoyaltyModal] = useState(false);
  const [showLoyaltyHistory, setShowLoyaltyHistory] = useState(false);
  const [editingPoints, setEditingPoints] = useState(false);
  const [pointsAdjustment, setPointsAdjustment] = useState({ points: 0, reason: '' });
  const [newCustomer, setNewCustomer] = useState({
    name: '',
    email: '',
    phone: '',
    address: '',
    note: ''
  });

  useEffect(() => {
    const fetchCustomers = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/customers');
        
        if (!response.ok) {
          throw new Error('Failed to fetch customers');
        }
        
        const data = await response.json();
        
        // Extract customers array from response
        const customersArray = data.customers || data;
        
        // Transform customer data to match the expected format
        const transformedCustomers = customersArray.map(customer => {
          // Calculate customer type based on total orders or spending
          let customerType = 'new';
          const totalOrders = customer.orders?.length || 0;
          const totalSpent = customer.orders?.reduce((sum, order) => sum + (order.total || 0), 0) || 0;
          
          if (totalSpent >= 50000000 || totalOrders >= 20) {
            customerType = 'vip';
          } else if (totalOrders >= 5) {
            customerType = 'regular';
          }
          
          // Get last order date
          const lastOrderDate = customer.orders && customer.orders.length > 0 
            ? customer.orders.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))[0].createdAt.split('T')[0]
            : null;
          
          // Determine status based on last order date
          const status = lastOrderDate && 
            new Date(lastOrderDate) > new Date(Date.now() - 90 * 24 * 60 * 60 * 1000) 
            ? 'active' : 'inactive';
          
          return {
            id: customer.id,
            name: customer.personalDetails?.name || customer.contactInfo?.name || 'Không có tên',
            email: customer.contactInfo?.email || customer.personalDetails?.email || 'Không có email',
            phone: customer.contactInfo?.phone || customer.personalDetails?.phone || 'Không có SĐT',
            address: customer.addresses?.[0]?.fullAddress || 
                    `${customer.addresses?.[0]?.street || ''} ${customer.addresses?.[0]?.district || ''} ${customer.addresses?.[0]?.city || ''}`.trim() ||
                    'Không có địa chỉ',
            customerType,
            totalOrders,
            totalSpent,
            lastOrderDate,
            joinDate: customer.createdAt ? customer.createdAt.split('T')[0] : new Date().toISOString().split('T')[0],
            status,
            note: customer.note || ''
          };
        });
        
        setCustomers(transformedCustomers);
      } catch (error) {
        console.error('Error fetching customers:', error);
        setCustomers([]);
      } finally {
        setLoading(false);
      }
    };
    
    fetchCustomers();
    fetchLoyaltyData();
  }, []);

  const fetchLoyaltyData = async () => {
    try {
      setLoyaltyLoading(true);
      const response = await fetch('/api/admin/loyalty-points');
      if (response.ok) {
        const data = await response.json();
        setLoyaltyData(data.customers || {});
      } else {
        console.error('Failed to fetch loyalty data');
        setLoyaltyData({});
      }
    } catch (error) {
      console.error('Error fetching loyalty data:', error);
      setLoyaltyData({});
    } finally {
      setLoyaltyLoading(false);
    }
  };

  // Loyalty helper functions
  const getCustomerLoyaltyInfo = (customerId) => {
    return loyaltyData[customerId] || {
      totalPoints: 0,
      availablePoints: 0,
      tier: 'Bronze',
      totalSpent: 0,
      transactions: []
    };
  };

  const getTierColor = (tier) => {
    switch (tier?.toLowerCase()) {
      case 'bronze': return 'text-orange-600 bg-orange-100';
      case 'silver': return 'text-gray-600 bg-gray-100';
      case 'gold': return 'text-yellow-600 bg-yellow-100';
      case 'platinum': return 'text-purple-600 bg-purple-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const adjustCustomerPoints = async (customerId, pointsChange, reason) => {
    try {
      const response = await fetch('/api/admin/loyalty-points', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'adjust',
          customerId: customerId,
          points: pointsChange,
          reason: reason
        })
      });

      if (response.ok) {
        const result = await response.json();
        // Refresh loyalty data
        await fetchLoyaltyData();
        setEditingPoints(false);
        setPointsAdjustment({ points: 0, reason: '' });
        return result;
      } else {
        throw new Error('Failed to adjust customer points');
      }
    } catch (error) {
      console.error('Error adjusting points:', error);
      alert('Lỗi khi điều chỉnh điểm: ' + error.message);
    }
  };

  const filteredCustomers = customers.filter(customer => {
    const matchesSearch = customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         customer.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         customer.phone.includes(searchTerm) ||
                         customer.id.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFilter = filterType === 'all' || customer.customerType === filterType;
    return matchesSearch && matchesFilter;
  });

  const getCustomerTypeColor = (type) => {
    switch (type) {
      case 'vip': return 'text-purple-600 bg-purple-100';
      case 'regular': return 'text-blue-600 bg-blue-100';
      case 'new': return 'text-green-600 bg-green-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getCustomerTypeText = (type) => {
    switch (type) {
      case 'vip': return 'VIP';
      case 'regular': return 'Thường';
      case 'new': return 'Mới';
      default: return 'Khác';
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return 'text-green-600 bg-green-100';
      case 'inactive': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'active': return 'Hoạt động';
      case 'inactive': return 'Không hoạt động';
      default: return 'Không xác định';
    }
  };

  const handleViewCustomer = (customer) => {
    setSelectedCustomer(customer);
    setShowCustomerDetail(true);
  };

  const handleAddCustomer = (e) => {
    e.preventDefault();
    const customer = {
      id: `CUST${String(customers.length + 1).padStart(3, '0')}`,
      ...newCustomer,
      customerType: 'new',
      totalOrders: 0,
      totalSpent: 0,
      lastOrderDate: null,
      joinDate: new Date().toISOString().split('T')[0],
      status: 'active'
    };
    
    setCustomers([customer, ...customers]);
    setNewCustomer({ name: '', email: '', phone: '', address: '', note: '' });
    setShowAddCustomer(false);
  };

  const customerStats = {
    total: customers.length,
    vip: customers.filter(c => c.customerType === 'vip').length,
    regular: customers.filter(c => c.customerType === 'regular').length,
    new: customers.filter(c => c.customerType === 'new').length,
    active: customers.filter(c => c.status === 'active').length,
    totalRevenue: customers.reduce((sum, c) => sum + c.totalSpent, 0),
    totalLoyaltyPoints: Object.values(loyaltyData).reduce((sum, loyalty) => sum + (loyalty.availablePoints || 0), 0),
    goldTier: Object.values(loyaltyData).filter(loyalty => loyalty.tier?.toLowerCase() === 'gold').length,
    platinumTier: Object.values(loyaltyData).filter(loyalty => loyalty.tier?.toLowerCase() === 'platinum').length
  };

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <button
            onClick={() => router.push(`/admin`)}
            className="flex items-center text-blue-600 hover:text-blue-800 mb-4 transition-colors"
          >
            <FiArrowLeft className="w-5 h-5 mr-2" />
            Quay lại trang chủ Admin
          </button>
          
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 flex items-center">
                <FiUsers className="w-8 h-8 mr-3 text-blue-600" />
                Quản lý khách hàng
              </h1>
              <p className="text-gray-600 mt-2">Theo dõi và quản lý thông tin khách hàng</p>
            </div>
            
            <div className="text-right">
              <div className="text-sm text-gray-500">Tổng khách hàng</div>
              <div className="text-2xl font-bold text-blue-600">{customerStats.total}</div>
            </div>
          </div>
        </div>

        {/* Thống kê nhanh */}
        <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">VIP</p>
                <p className="text-2xl font-bold text-purple-600">{customerStats.vip}</p>
              </div>
              <FiStar className="w-8 h-8 text-purple-600" />
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Thường</p>
                <p className="text-2xl font-bold text-blue-600">{customerStats.regular}</p>
              </div>
              <FiUser className="w-8 h-8 text-blue-600" />
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Mới</p>
                <p className="text-2xl font-bold text-green-600">{customerStats.new}</p>
              </div>
              <FiUserPlus className="w-8 h-8 text-green-600" />
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Hoạt động</p>
                <p className="text-2xl font-bold text-green-600">{customerStats.active}</p>
              </div>
              <FiUsers className="w-8 h-8 text-green-600" />
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Tổng chi tiêu</p>
                <p className="text-2xl font-bold text-green-600">
                  {customerStats.totalRevenue.toLocaleString('vi-VN')}đ
                </p>
              </div>
              <FiDollarSign className="w-8 h-8 text-green-600" />
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Tổng điểm</p>
                <p className="text-2xl font-bold text-purple-600">
                  {customerStats.totalLoyaltyPoints.toLocaleString('vi-VN')}
                </p>
              </div>
              <FiGift className="w-8 h-8 text-purple-600" />
            </div>
          </div>
        </div>

        {/* Danh sách khách hàng */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <h2 className="text-xl font-semibold text-gray-900">Danh sách khách hàng</h2>
              
              <div className="flex flex-col sm:flex-row gap-3">
                <div className="relative">
                  <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <input
                    type="text"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Tìm kiếm khách hàng..."
                  />
                </div>
                
                <div className="relative">
                  <FiFilter className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <select
                    value={filterType}
                    onChange={(e) => setFilterType(e.target.value)}
                    className="pl-10 pr-8 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 appearance-none"
                  >
                    <option value="all">Tất cả loại</option>
                    <option value="vip">VIP</option>
                    <option value="regular">Thường</option>
                    <option value="new">Mới</option>
                  </select>
                </div>
                
                <button
                  onClick={() => {
                    fetchLoyaltyData();
                  }}
                  className="flex items-center px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors"
                  disabled={loyaltyLoading}
                >
                  <FiGift className="w-4 h-4 mr-2" />
                  {loyaltyLoading ? 'Đang tải...' : 'Làm mới điểm'}
                </button>
                <button
                  onClick={() => setShowAddCustomer(true)}
                  className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                >
                  <FiUserPlus className="w-4 h-4 mr-2" />
                  Thêm khách hàng
                </button>
                
                <button className="flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors">
                  <FiDownload className="w-4 h-4 mr-2" />
                  Xuất Excel
                </button>
              </div>
            </div>
          </div>
          
          <div>
            {loading ? (
              <div className="p-8 text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="text-gray-500 mt-2">Đang tải dữ liệu...</p>
              </div>
            ) : filteredCustomers.length === 0 ? (
              <div className="p-8 text-center text-gray-500">
                <FiUsers className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                <p>Không có khách hàng nào</p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-4">
                {filteredCustomers.map((customer) => (
                  <div key={customer.id} className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                    {/* Header with avatar and basic info */}
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-12 w-12">
                          <div className="h-12 w-12 rounded-full bg-blue-100 flex items-center justify-center">
                            <FiUser className="w-6 h-6 text-blue-600" />
                          </div>
                        </div>
                        <div className="ml-3">
                          <div className="text-sm font-medium text-gray-900">{customer.name}</div>
                          <div className="text-xs text-blue-600 font-medium">{customer.id}</div>
                        </div>
                      </div>
                      <div className="flex space-x-1">
                        <button
                          onClick={() => handleViewCustomer(customer)}
                          className="text-blue-600 hover:text-blue-900 transition-colors p-1"
                          title="Xem chi tiết"
                        >
                          <FiEye className="w-4 h-4" />
                        </button>
                        <button
                          className="text-green-600 hover:text-green-900 transition-colors p-1"
                          title="Chỉnh sửa"
                        >
                          <FiEdit className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => {
                            setSelectedCustomer(customer);
                            setShowLoyaltyModal(true);
                          }}
                          className="text-purple-600 hover:text-purple-900 transition-colors p-1"
                          title="Quản lý điểm loyalty"
                        >
                          <FiGift className="w-4 h-4" />
                        </button>
                      </div>
                    </div>

                    {/* Customer type and status */}
                    <div className="flex items-center justify-between mb-3">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getCustomerTypeColor(customer.customerType)}`}>
                        {customer.customerType === 'vip' && <FiStar className="w-3 h-3 mr-1" />}
                        {getCustomerTypeText(customer.customerType)}
                      </span>
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(customer.status)}`}>
                        {getStatusText(customer.status)}
                      </span>
                    </div>

                    {/* Contact info */}
                    <div className="space-y-2 mb-3">
                      <div className="text-sm text-gray-900 flex items-center">
                        <FiPhone className="w-4 h-4 mr-2 text-gray-400 flex-shrink-0" />
                        <span className="truncate">{customer.phone}</span>
                      </div>
                      <div className="text-sm text-gray-500 flex items-center">
                        <FiMail className="w-4 h-4 mr-2 text-gray-400 flex-shrink-0" />
                        <span className="truncate">{customer.email}</span>
                      </div>
                    </div>

                    {/* Stats */}
                    <div className="grid grid-cols-3 gap-2 pt-3 border-t border-gray-100">
                      <div className="text-center">
                        <div className="text-sm font-semibold text-gray-900">{customer.totalOrders}</div>
                        <div className="text-xs text-gray-500">Đơn hàng</div>
                      </div>
                      <div className="text-center">
                        <div className="text-sm font-semibold text-green-600">
                          {customer.totalSpent.toLocaleString('vi-VN')}đ
                        </div>
                        <div className="text-xs text-gray-500">Chi tiêu</div>
                      </div>
                      <div className="text-center">
                        <div className="text-sm font-semibold text-purple-600">
                          {getCustomerLoyaltyInfo(customer.id).availablePoints?.toLocaleString() || 0}
                        </div>
                        <div className="text-xs text-gray-500">Điểm</div>
                      </div>
                    </div>

                    {/* Loyalty tier */}
                    <div className="mt-2 flex items-center justify-center">
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getTierColor(getCustomerLoyaltyInfo(customer.id).tier)}`}>
                        <FiGift className="w-3 h-3 mr-1" />
                        {getCustomerLoyaltyInfo(customer.id).tier}
                      </span>
                    </div>

                    {/* Join date and last order */}
                    <div className="mt-3 pt-3 border-t border-gray-100">
                      <div className="text-xs text-gray-500 flex items-center justify-between">
                        <span className="flex items-center">
                          <FiCalendar className="w-3 h-3 mr-1" />
                          Tham gia: {customer.joinDate}
                        </span>
                        {customer.lastOrderDate && (
                          <span>Đơn cuối: {customer.lastOrderDate}</span>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Modal chi tiết khách hàng */}
        {showCustomerDetail && selectedCustomer && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
              <div className="p-6 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <h3 className="text-xl font-semibold text-gray-900">
                    Chi tiết khách hàng {selectedCustomer.id}
                  </h3>
                  <button
                    onClick={() => setShowCustomerDetail(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    ×
                  </button>
                </div>
              </div>
              
              <div className="p-6 space-y-6">
                {/* Thông tin cơ bản */}
                <div>
                  <h4 className="text-lg font-medium text-gray-900 mb-3">Thông tin cơ bản</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Tên khách hàng</label>
                      <p className="mt-1 text-sm text-gray-900">{selectedCustomer.name}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Loại khách hàng</label>
                      <span className={`mt-1 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getCustomerTypeColor(selectedCustomer.customerType)}`}>
                        {selectedCustomer.customerType === 'vip' && <FiStar className="w-3 h-3 mr-1" />}
                        {getCustomerTypeText(selectedCustomer.customerType)}
                      </span>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Email</label>
                      <p className="mt-1 text-sm text-gray-900">{selectedCustomer.email}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Số điện thoại</label>
                      <p className="mt-1 text-sm text-gray-900">{selectedCustomer.phone}</p>
                    </div>
                    <div className="md:col-span-2">
                      <label className="block text-sm font-medium text-gray-700">Địa chỉ</label>
                      <p className="mt-1 text-sm text-gray-900">{selectedCustomer.address}</p>
                    </div>
                  </div>
                </div>
                
                {/* Thống kê mua hàng */}
                <div>
                  <h4 className="text-lg font-medium text-gray-900 mb-3">Thống kê mua hàng</h4>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="bg-blue-50 p-4 rounded-lg">
                      <div className="flex items-center">
                        <FiShoppingBag className="w-8 h-8 text-blue-600 mr-3" />
                        <div>
                          <p className="text-sm text-gray-600">Tổng đơn hàng</p>
                          <p className="text-2xl font-bold text-blue-600">{selectedCustomer.totalOrders}</p>
                        </div>
                      </div>
                    </div>
                    <div className="bg-green-50 p-4 rounded-lg">
                      <div className="flex items-center">
                        <FiDollarSign className="w-8 h-8 text-green-600 mr-3" />
                        <div>
                          <p className="text-sm text-gray-600">Tổng chi tiêu</p>
                          <p className="text-2xl font-bold text-green-600">
                            {selectedCustomer.totalSpent.toLocaleString('vi-VN')}đ
                          </p>
                        </div>
                      </div>
                    </div>
                    <div className="bg-purple-50 p-4 rounded-lg">
                      <div className="flex items-center">
                        <FiCalendar className="w-8 h-8 text-purple-600 mr-3" />
                        <div>
                          <p className="text-sm text-gray-600">Đơn hàng cuối</p>
                          <p className="text-lg font-bold text-purple-600">
                            {selectedCustomer.lastOrderDate || 'Chưa có'}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                
                {/* Ghi chú */}
                <div>
                  <h4 className="text-lg font-medium text-gray-900 mb-3">Ghi chú</h4>
                  <p className="text-sm text-gray-900 bg-gray-50 p-3 rounded-md">
                    {selectedCustomer.note || 'Không có ghi chú'}
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Modal thêm khách hàng */}
        {showAddCustomer && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div className="bg-white rounded-lg max-w-2xl w-full">
              <div className="p-6 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <h3 className="text-xl font-semibold text-gray-900">Thêm khách hàng mới</h3>
                  <button
                    onClick={() => setShowAddCustomer(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    ×
                  </button>
                </div>
              </div>
              
              <form onSubmit={handleAddCustomer} className="p-6 space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Tên khách hàng *
                    </label>
                    <input
                      type="text"
                      value={newCustomer.name}
                      onChange={(e) => setNewCustomer({...newCustomer, name: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Số điện thoại *
                    </label>
                    <input
                      type="tel"
                      value={newCustomer.phone}
                      onChange={(e) => setNewCustomer({...newCustomer, phone: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Email
                  </label>
                  <input
                    type="email"
                    value={newCustomer.email}
                    onChange={(e) => setNewCustomer({...newCustomer, email: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Địa chỉ
                  </label>
                  <input
                    type="text"
                    value={newCustomer.address}
                    onChange={(e) => setNewCustomer({...newCustomer, address: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Ghi chú
                  </label>
                  <textarea
                    value={newCustomer.note}
                    onChange={(e) => setNewCustomer({...newCustomer, note: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    rows="3"
                  />
                </div>
                
                <div className="flex justify-end space-x-3 pt-4">
                  <button
                    type="button"
                    onClick={() => setShowAddCustomer(false)}
                    className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
                  >
                    Hủy
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                  >
                    Thêm khách hàng
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}

        {/* Loyalty Management Modal */}
        {showLoyaltyModal && selectedCustomer && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
              <div className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-xl font-semibold text-gray-900">
                    Quản lý điểm loyalty - {selectedCustomer.name}
                  </h3>
                  <button
                    onClick={() => {
                      setShowLoyaltyModal(false);
                      setEditingPoints(false);
                      setPointsAdjustment({ points: 0, reason: '' });
                    }}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <FiX className="text-xl" />
                  </button>
                </div>

                {/* Customer Info */}
                <div className="bg-gray-50 rounded-lg p-4 mb-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Tên khách hàng</label>
                      <p className="text-gray-900">{selectedCustomer.name}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
                      <p className="text-gray-900">{selectedCustomer.email}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Số điện thoại</label>
                      <p className="text-gray-900">{selectedCustomer.phone}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">ID khách hàng</label>
                      <p className="text-gray-900 font-mono text-sm">{selectedCustomer.id}</p>
                    </div>
                  </div>
                </div>

                {/* Loyalty Info */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                  <div className="bg-blue-50 rounded-lg p-4">
                    <h4 className="font-medium text-blue-900 mb-2">Điểm loyalty hiện tại</h4>
                    <p className="text-2xl font-bold text-blue-600">
                      {getCustomerLoyaltyInfo(selectedCustomer.id).availablePoints?.toLocaleString() || 0} điểm
                    </p>
                    <p className="text-sm text-blue-700 mt-1">
                      Tổng tích lũy: {getCustomerLoyaltyInfo(selectedCustomer.id).totalPoints?.toLocaleString() || 0} điểm
                    </p>
                  </div>
                  <div className="bg-purple-50 rounded-lg p-4">
                    <h4 className="font-medium text-purple-900 mb-2">Hạng khách hàng</h4>
                    <p className={`text-xl font-bold px-3 py-1 rounded-full inline-block ${getTierColor(getCustomerLoyaltyInfo(selectedCustomer.id).tier)}`}>
                      <FiStar className="inline mr-1" />
                      {getCustomerLoyaltyInfo(selectedCustomer.id).tier}
                    </p>
                    <p className="text-sm text-purple-700 mt-2">
                      Tổng chi tiêu: {selectedCustomer.totalSpent?.toLocaleString() || 0} VND
                    </p>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex space-x-3 mb-6">
                  <button
                    onClick={() => setEditingPoints(true)}
                    className="flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
                  >
                    <FiEdit className="mr-2" /> Điều chỉnh điểm
                  </button>
                  <button
                    onClick={() => {
                      setShowLoyaltyHistory(true);
                    }}
                    className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                  >
                    <FiClock className="mr-2" /> Lịch sử giao dịch
                  </button>
                </div>

                {/* Points Adjustment */}
                {editingPoints && (
                  <div className="border-t pt-6">
                    <h4 className="font-medium text-gray-900 mb-4">Điều chỉnh điểm</h4>
                    <div className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Số điểm thay đổi (+ hoặc -)
                          </label>
                          <input
                            type="number"
                            value={pointsAdjustment.points}
                            onChange={(e) => setPointsAdjustment(prev => ({ ...prev, points: parseInt(e.target.value) || 0 }))}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            placeholder="Nhập số điểm (VD: 100 hoặc -50)"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Lý do điều chỉnh
                          </label>
                          <input
                            type="text"
                            value={pointsAdjustment.reason}
                            onChange={(e) => setPointsAdjustment(prev => ({ ...prev, reason: e.target.value }))}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            placeholder="Nhập lý do điều chỉnh"
                          />
                        </div>
                      </div>

                      <div className="flex space-x-3">
                        <button
                          onClick={() => {
                            if (pointsAdjustment.points !== 0 && pointsAdjustment.reason.trim()) {
                              adjustCustomerPoints(selectedCustomer.id, pointsAdjustment.points, pointsAdjustment.reason);
                            } else {
                              alert('Vui lòng nhập số điểm và lý do điều chỉnh');
                            }
                          }}
                          className="flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
                        >
                          <FiSave className="mr-2" /> Lưu thay đổi
                        </button>
                        <button
                          onClick={() => {
                            setEditingPoints(false);
                            setPointsAdjustment({ points: 0, reason: '' });
                          }}
                          className="flex items-center px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
                        >
                          <FiX className="mr-2" /> Hủy
                        </button>
                      </div>

                      {pointsAdjustment.points !== 0 && (
                        <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3">
                          <p className="text-sm text-yellow-800">
                            <strong>Xem trước:</strong> Điểm sau khi điều chỉnh sẽ là{' '}
                            <span className="font-bold">
                              {(getCustomerLoyaltyInfo(selectedCustomer.id).availablePoints + pointsAdjustment.points).toLocaleString()}
                            </span> điểm
                          </p>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Loyalty History Modal */}
        {showLoyaltyHistory && selectedCustomer && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
              <div className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-xl font-semibold text-gray-900">
                    Lịch sử giao dịch điểm - {selectedCustomer.name}
                  </h3>
                  <button
                    onClick={() => setShowLoyaltyHistory(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <FiX className="text-xl" />
                  </button>
                </div>

                <div className="space-y-4">
                  {getCustomerLoyaltyInfo(selectedCustomer.id).transactions?.length > 0 ? (
                    getCustomerLoyaltyInfo(selectedCustomer.id).transactions.map((transaction, index) => (
                      <div key={index} className="border border-gray-200 rounded-lg p-4">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <div className={`w-8 h-8 rounded-full flex items-center justify-center mr-3 ${
                              transaction.points > 0 ? 'bg-green-100 text-green-600' : 'bg-red-100 text-red-600'
                            }`}>
                              {transaction.points > 0 ? <FiPlus /> : <FiMinus />}
                            </div>
                            <div>
                              <div className="font-medium text-gray-900">
                                {transaction.points > 0 ? '+' : ''}{transaction.points} điểm
                              </div>
                              <div className="text-sm text-gray-500">
                                {transaction.type} - {transaction.description}
                              </div>
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="text-sm text-gray-500">
                              {new Date(transaction.createdAt).toLocaleDateString('vi-VN')}
                            </div>
                            <div className="text-sm text-gray-500">
                              {new Date(transaction.createdAt).toLocaleTimeString('vi-VN')}
                            </div>
                          </div>
                        </div>
                        {transaction.orderId && (
                          <div className="mt-2 text-sm text-gray-500">
                            Đơn hàng: {transaction.orderId}
                          </div>
                        )}
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-8 text-gray-500">
                      Chưa có giao dịch điểm nào
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

const CustomersManagement = () => {
  return (
    <AdminLayout>
      <CustomersManagementContent />
    </AdminLayout>
  );
};

export default CustomersManagement;