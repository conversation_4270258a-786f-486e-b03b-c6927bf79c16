import React, { useState, useEffect } from 'react';
import { FaUser, FaPlus, FaRefresh, FaSearch, FaEye, FaEdit, FaFileAlt, FaCheck, FaTimes, FaExclamationTriangle, FaClock, FaEnvelope, FaPhone, FaMapMarkerAlt, FaStar, FaShieldAlt, FaDownload, FaUpload } from 'react-icons/fa';
import { FiAlertCircle } from 'react-icons/fi';
import AdminLayout from '../../components/admin/AdminLayout';

const UnifiedCustomerManagementContent = () => {
  const [customers, setCustomers] = useState([]);
  const [loyaltyData, setLoyaltyData] = useState({});
  const [error, setError] = useState(null);
  
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [filterCompliance, setFilterCompliance] = useState('all');
  const [selectedCustomer, setSelectedCustomer] = useState(null);
  const [showCustomerDetail, setShowCustomerDetail] = useState(false);
  const [showAddCustomer, setShowAddCustomer] = useState(false);
  const [showDocumentModal, setShowDocumentModal] = useState(false);

  // Fetch customer data from API
  useEffect(() => {
    const fetchCustomers = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // Fetch customers from customer-approval API
        const customerResponse = await fetch('/api/admin/customer-approval?filter=all');
        if (!customerResponse.ok) {
          throw new Error('Failed to fetch customers');
        }
        const customerData = await customerResponse.json();
        
        // Fetch loyalty data
        const loyaltyResponse = await fetch('/api/admin/loyalty-points');
        if (!loyaltyResponse.ok) {
          throw new Error('Failed to fetch loyalty data');
        }
        const loyaltyResult = await loyaltyResponse.json();
        
        setLoyaltyData(loyaltyResult.customers || {});
        
        // Transform customer data to match component structure
        const transformedCustomers = customerData.customers.map(customer => {
          const loyaltyInfo = loyaltyResult.customers[customer.id] || {
            totalPoints: 0,
            availablePoints: 0,
            tier: 'Bronze',
            totalSpent: 0
          };
          
          // Extract personal details
          const personalDetails = customer.personalDetails || {};
          const contactInfo = customer.contactInfo || {};
          const addresses = customer.addresses || [];
          
          // Calculate document compliance
          const documents = customer.documents || [];
          const documentCompliance = {
            overall: customer.approvalStatus ? 
              (Object.values(customer.approvalStatus).every(status => status === 'approved') ? 'complete' :
               Object.values(customer.approvalStatus).some(status => status === 'pending') ? 'partial' : 'incomplete') : 'incomplete',
            required: documents.length || 0,
            submitted: documents.length || 0,
            approved: documents.filter(doc => doc.status === 'approved').length || 0,
            pending: documents.filter(doc => doc.status === 'pending').length || 0,
            rejected: documents.filter(doc => doc.status === 'rejected').length || 0
          };
          
          return {
            id: customer.id,
            personalDetails: {
              firstName: personalDetails.firstName || personalDetails.name?.split(' ')[0] || '',
              lastName: personalDetails.lastName || personalDetails.name?.split(' ').slice(1).join(' ') || '',
              dateOfBirth: personalDetails.dateOfBirth || '',
              gender: personalDetails.gender || '',
              occupation: personalDetails.occupation || '',
              nationality: personalDetails.nationality || ''
            },
            contactInfo: {
              email: contactInfo.email || contactInfo.primaryEmail || '',
              phone: contactInfo.phone || contactInfo.primaryPhone || '',
              alternatePhone: contactInfo.alternatePhone || contactInfo.secondaryPhone || ''
            },
            addresses: addresses.map(addr => ({
              id: addr.id || 1,
              type: addr.type || 'home',
              street: addr.street || addr.address || '',
              ward: addr.ward || '',
              district: addr.district || '',
              city: addr.city || '',
              state: addr.state || '',
              zipCode: addr.zipCode || '',
              country: addr.country || 'Việt Nam',
              isDefault: addr.isDefault || true
            })),
            customerType: customer.customerType || 'regular',
            status: customer.status || 'active',
            loyaltyTier: loyaltyInfo.tier || 'Bronze',
            loyaltyPoints: loyaltyInfo.availablePoints || 0,
            totalOrders: customer.totalOrders || 0,
            totalSpent: loyaltyInfo.totalSpent || 0,
            joinDate: customer.createdAt ? customer.createdAt.split('T')[0] : new Date().toISOString().split('T')[0],
            documents: documents.map(doc => ({
              id: doc.id,
              documentType: doc.type || doc.documentType || 'unknown',
              fileName: doc.fileName || doc.name || 'document.pdf',
              uploadDate: doc.uploadDate || doc.createdAt || new Date().toISOString().split('T')[0],
              status: doc.status || 'pending',
              fileSize: doc.fileSize || 0,
              type: doc.type || doc.documentType || 'Document',
              notes: doc.notes || ''
            })),
            documentCompliance
          };
        });
        
        setCustomers(transformedCustomers);
      } catch (err) {
        console.error('Error fetching customer data:', err);
        setError(err.message);
        setCustomers([]);
      } finally {
        setLoading(false);
      }
    };
    
    fetchCustomers();
  }, []);

  // Filter customers based on search and filters
  const filteredCustomers = customers.filter(customer => {
    const matchesSearch = searchTerm === '' || 
      `${customer.personalDetails.firstName} ${customer.personalDetails.lastName}`.toLowerCase().includes(searchTerm.toLowerCase()) ||
      customer.contactInfo.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      customer.contactInfo.phone.includes(searchTerm);
    
    const matchesStatus = filterStatus === 'all' || customer.status === filterStatus;
    const matchesCompliance = filterCompliance === 'all' || customer.documentCompliance.overall === filterCompliance;
    
    return matchesSearch && matchesStatus && matchesCompliance;
  });

  // Calculate statistics
  const stats = {
    total: customers.length,
    active: customers.filter(c => c.status === 'active').length,
    vip: customers.filter(c => c.customerType === 'VIP').length,
    compliant: customers.filter(c => c.documentCompliance.overall === 'complete').length,
    pending: customers.filter(c => c.documentCompliance.pending > 0).length
  };

  const getComplianceColor = (status) => {
    switch (status) {
      case 'complete': return 'text-green-600 bg-green-100';
      case 'partial': return 'text-yellow-600 bg-yellow-100';
      case 'incomplete': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getComplianceIcon = (status) => {
    switch (status) {
      case 'complete': return <FaCheck className="w-4 h-4" />;
      case 'partial': return <FaClock className="w-4 h-4" />;
      case 'incomplete': return <FaExclamationTriangle className="w-4 h-4" />;
      default: return <FaTimes className="w-4 h-4" />;
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  // Handler functions
  const handleViewCustomer = (customer) => {
    setSelectedCustomer(customer);
    setShowCustomerDetail(true);
  };

  const handleViewDocuments = (customer) => {
    setSelectedCustomer(customer);
    setShowDocumentModal(true);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        {error && (
          <div className="p-4 bg-red-50 border-l-4 border-red-400">
            <div className="flex">
              <div className="flex-shrink-0">
                <FiAlertCircle className="h-5 w-5 text-red-400" />
              </div>
              <div className="ml-3">
                <p className="text-sm text-red-700">
                  Error loading customer data: {error}
                </p>
              </div>
            </div>
          </div>
        )}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Quản lý khách hàng tổng hợp</h1>
              <p className="mt-1 text-sm text-gray-500">
                Quản lý thông tin khách hàng và tài liệu trong một giao diện thống nhất
              </p>
            </div>
            <div className="flex space-x-3">
              <button 
                onClick={() => setLoading(true)}
                className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                <FaRefresh className="mr-2" />
                Làm mới
              </button>
              <button 
                onClick={() => setShowAddCustomer(true)}
                className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
              >
                <FaPlus className="mr-2" />
                Thêm khách hàng
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Statistics */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-6">
          <div className="bg-white p-4 rounded-lg shadow-sm border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Customers</p>
                <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
              </div>
              <FaUser className="w-8 h-8 text-blue-600" />
            </div>
          </div>
          <div className="bg-white p-4 rounded-lg shadow-sm border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Active</p>
                <p className="text-2xl font-bold text-green-600">{stats.active}</p>
              </div>
              <FaCheck className="w-8 h-8 text-green-600" />
            </div>
          </div>
          <div className="bg-white p-4 rounded-lg shadow-sm border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">VIP Members</p>
                <p className="text-2xl font-bold text-purple-600">{stats.vip}</p>
              </div>
              <FaStar className="w-8 h-8 text-purple-600" />
            </div>
          </div>
          <div className="bg-white p-4 rounded-lg shadow-sm border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Compliant</p>
                <p className="text-2xl font-bold text-green-600">{stats.compliant}</p>
              </div>
              <FaShieldAlt className="w-8 h-8 text-green-600" />
            </div>
          </div>
          <div className="bg-white p-4 rounded-lg shadow-sm border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Pending Docs</p>
                <p className="text-2xl font-bold text-yellow-600">{stats.pending}</p>
              </div>
              <FaClock className="w-8 h-8 text-yellow-600" />
            </div>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="bg-white rounded-lg shadow-sm border p-4 mb-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="relative">
              <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search customers..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">All Status</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
              <option value="suspended">Suspended</option>
            </select>
            <select
              value={filterCompliance}
              onChange={(e) => setFilterCompliance(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">All Compliance</option>
              <option value="complete">Complete</option>
              <option value="partial">Partial</option>
              <option value="incomplete">Incomplete</option>
            </select>
            <div className="text-sm text-gray-600 flex items-center">
              Showing {filteredCustomers.length} of {customers.length} customers
            </div>
          </div>
        </div>

        {/* Customer List */}
        <div className="bg-white rounded-lg shadow">
          <div className="p-4 border-b">
            <h2 className="text-lg font-semibold text-gray-800">Customer List</h2>
          </div>
          <div className="p-4">
            {loading ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="mt-2 text-gray-600">Loading customers...</p>
              </div>
            ) : filteredCustomers.length === 0 ? (
              <div className="text-center py-8">
                <FaUser className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600">No customers found</p>
              </div>
            ) : (
              <div className="divide-y divide-gray-200">
                {filteredCustomers.map((customer) => (
                  <div key={customer.id} className="p-6 hover:bg-gray-50">
                    <div className="flex items-center justify-between">
                      <div className="flex items-start space-x-4">
                        <div className="flex-shrink-0">
                          <div className="h-12 w-12 rounded-full bg-gray-300 flex items-center justify-center">
                            <FaUser className="h-6 w-6 text-gray-600" />
                          </div>
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center space-x-3 mb-2">
                            <h3 className="text-lg font-medium text-gray-900">
                              {customer.personalDetails.firstName} {customer.personalDetails.lastName}
                            </h3>
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                              customer.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                            }`}>
                              {customer.status}
                            </span>
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                              {customer.customerType}
                            </span>
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                              {customer.loyaltyTier}
                            </span>
                          </div>
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600">
                            <div className="flex items-center space-x-2">
                              <FaEnvelope className="w-4 h-4" />
                              <span>{customer.contactInfo.email}</span>
                            </div>
                            <div className="flex items-center space-x-2">
                              <FaPhone className="w-4 h-4" />
                              <span>{customer.contactInfo.phone}</span>
                            </div>
                            <div className="flex items-center space-x-2">
                              <FaMapMarkerAlt className="w-4 h-4" />
                              <span>{customer.addresses[0]?.city}</span>
                            </div>
                          </div>
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-2 text-sm">
                            <div>
                              <span className="text-gray-600">Loyalty Points: </span>
                              <span className="font-medium text-purple-600">{customer.loyaltyPoints.toLocaleString()}</span>
                            </div>
                            <div>
                              <span className="text-gray-600">Total Orders: </span>
                              <span className="font-medium">{customer.totalOrders}</span>
                            </div>
                            <div>
                              <span className="text-gray-600">Total Spent: </span>
                              <span className="font-medium text-green-600">{formatCurrency(customer.totalSpent)}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div className="flex flex-col items-end space-y-3">
                        {/* Document Compliance Status */}
                        <div className={`flex items-center space-x-2 px-3 py-1 rounded-full text-sm font-medium ${
                          getComplianceColor(customer.documentCompliance.overall)
                        }`}>
                          {getComplianceIcon(customer.documentCompliance.overall)}
                          <span className="capitalize">{customer.documentCompliance.overall}</span>
                        </div>
                        <div className="text-xs text-gray-500 text-right">
                          <div>Docs: {customer.documentCompliance.approved}/{customer.documentCompliance.required}</div>
                          {customer.documentCompliance.pending > 0 && (
                            <div className="text-yellow-600">{customer.documentCompliance.pending} pending</div>
                          )}
                          {customer.documentCompliance.rejected > 0 && (
                            <div className="text-red-600">{customer.documentCompliance.rejected} rejected</div>
                          )}
                        </div>
                        {/* Action Buttons */}
                        <div className="flex space-x-2">
                          <button
                            onClick={() => handleViewCustomer(customer)}
                            className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                            title="View Details"
                          >
                            <FaEye className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => handleViewDocuments(customer)}
                            className="p-2 text-green-600 hover:bg-green-50 rounded-lg transition-colors"
                            title="Manage Documents"
                          >
                            <FaFileAlt className="w-4 h-4" />
                          </button>
                          <button
                            className="p-2 text-gray-600 hover:bg-gray-50 rounded-lg transition-colors"
                            title="Edit Customer"
                          >
                            <FaEdit className="w-4 h-4" />
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Customer Details Modal */}
        {showCustomerDetail && selectedCustomer && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
              <div className="p-6 border-b">
                <div className="flex items-center justify-between">
                  <h2 className="text-xl font-semibold text-gray-800">
                    Customer Details - {selectedCustomer.personalDetails.firstName} {selectedCustomer.personalDetails.lastName}
                  </h2>
                  <button
                    onClick={() => setShowCustomerDetail(false)}
                    className="p-2 hover:bg-gray-100 rounded-lg"
                  >
                    <FaTimes className="w-5 h-5 text-gray-500" />
                  </button>
                </div>
              </div>
              <div className="p-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Personal Details */}
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <h3 className="text-lg font-medium text-gray-800 mb-4 flex items-center">
                      <FaUser className="w-5 h-5 mr-2" />
                      Personal Information
                    </h3>
                    <div className="space-y-3">
                      <div>
                        <label className="text-sm font-medium text-gray-600">Full Name</label>
                        <p className="text-gray-800">{selectedCustomer.personalDetails.firstName} {selectedCustomer.personalDetails.lastName}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-600">Date of Birth</label>
                        <p className="text-gray-800">{selectedCustomer.personalDetails.dateOfBirth}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-600">Gender</label>
                        <p className="text-gray-800">{selectedCustomer.personalDetails.gender}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-600">Nationality</label>
                        <p className="text-gray-800">{selectedCustomer.personalDetails.nationality}</p>
                      </div>
                    </div>
                  </div>

                  {/* Contact Information */}
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <h3 className="text-lg font-medium text-gray-800 mb-4 flex items-center">
                      <FaEnvelope className="w-5 h-5 mr-2" />
                      Contact Information
                    </h3>
                    <div className="space-y-3">
                      <div>
                        <label className="text-sm font-medium text-gray-600">Email</label>
                        <p className="text-gray-800">{selectedCustomer.contactInfo.email}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-600">Phone</label>
                        <p className="text-gray-800">{selectedCustomer.contactInfo.phone}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-600">Alternative Phone</label>
                        <p className="text-gray-800">{selectedCustomer.contactInfo.alternativePhone || 'N/A'}</p>
                      </div>
                    </div>
                  </div>

                  {/* Address Information */}
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <h3 className="text-lg font-medium text-gray-800 mb-4 flex items-center">
                      <FaMapMarkerAlt className="w-5 h-5 mr-2" />
                      Address Information
                    </h3>
                    {selectedCustomer.addresses.map((address, index) => (
                      <div key={index} className="mb-4 last:mb-0">
                        <div className="text-sm font-medium text-gray-600 mb-2">
                          {address.type} Address
                        </div>
                        <div className="text-gray-800">
                          <p>{address.street}</p>
                          <p>{address.city}, {address.state} {address.zipCode}</p>
                          <p>{address.country}</p>
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* Loyalty Information */}
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <h3 className="text-lg font-medium text-gray-800 mb-4 flex items-center">
                      <FaStar className="w-5 h-5 mr-2" />
                      Loyalty Information
                    </h3>
                    <div className="space-y-3">
                      <div>
                        <label className="text-sm font-medium text-gray-600">Tier</label>
                        <p className="text-gray-800">{selectedCustomer.loyaltyTier}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-600">Points</label>
                        <p className="text-gray-800">{selectedCustomer.loyaltyPoints.toLocaleString()}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-600">Total Orders</label>
                        <p className="text-gray-800">{selectedCustomer.totalOrders}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-600">Total Spent</label>
                        <p className="text-gray-800">{formatCurrency(selectedCustomer.totalSpent)}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Document Management Modal */}
        {showDocumentModal && selectedCustomer && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg shadow-xl max-w-6xl w-full mx-4 max-h-[90vh] overflow-y-auto">
              <div className="p-6 border-b">
                <div className="flex items-center justify-between">
                  <h2 className="text-xl font-semibold text-gray-800">
                    Document Management - {selectedCustomer.personalDetails.firstName} {selectedCustomer.personalDetails.lastName}
                  </h2>
                  <button
                    onClick={() => setShowDocumentModal(false)}
                    className="p-2 hover:bg-gray-100 rounded-lg"
                  >
                    <FaTimes className="w-5 h-5 text-gray-500" />
                  </button>
                </div>
              </div>
              <div className="p-6">
                {/* Document Compliance Overview */}
                <div className="bg-gray-50 p-4 rounded-lg mb-6">
                  <h3 className="text-lg font-medium text-gray-800 mb-4">Compliance Overview</h3>
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-blue-600">{selectedCustomer.documentCompliance.required}</div>
                      <div className="text-sm text-gray-600">Required</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-600">{selectedCustomer.documentCompliance.approved}</div>
                      <div className="text-sm text-gray-600">Approved</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-yellow-600">{selectedCustomer.documentCompliance.pending}</div>
                      <div className="text-sm text-gray-600">Pending</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-red-600">{selectedCustomer.documentCompliance.rejected}</div>
                      <div className="text-sm text-gray-600">Rejected</div>
                    </div>
                  </div>
                </div>

                {/* Document List */}
                <div className="space-y-4">
                  <h3 className="text-lg font-medium text-gray-800">Documents</h3>
                  {selectedCustomer.documents.map((doc) => (
                    <div key={doc.id} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                          <FaFileAlt className="w-8 h-8 text-blue-600" />
                          <div>
                            <h4 className="font-medium text-gray-800">{doc.type}</h4>
                            <p className="text-sm text-gray-600">{doc.fileName}</p>
                            <p className="text-xs text-gray-500">Uploaded: {doc.uploadDate}</p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-4">
                          <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                            doc.status === 'approved' ? 'bg-green-100 text-green-800' :
                            doc.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                            'bg-red-100 text-red-800'
                          }`}>
                            {doc.status}
                          </span>
                          <div className="flex space-x-2">
                            <button className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg">
                              <FaEye className="w-4 h-4" />
                            </button>
                            <button className="p-2 text-green-600 hover:bg-green-50 rounded-lg">
                              <FaDownload className="w-4 h-4" />
                            </button>
                            {doc.status === 'pending' && (
                              <>
                                <button className="p-2 text-green-600 hover:bg-green-50 rounded-lg">
                                  <FaCheck className="w-4 h-4" />
                                </button>
                                <button className="p-2 text-red-600 hover:bg-red-50 rounded-lg">
                                  <FaTimes className="w-4 h-4" />
                                </button>
                              </>
                            )}
                          </div>
                        </div>
                      </div>
                      {doc.notes && (
                        <div className="mt-3 p-3 bg-gray-50 rounded">
                          <p className="text-sm text-gray-700">{doc.notes}</p>
                        </div>
                      )}
                    </div>
                  ))}
                </div>

                {/* Upload New Document */}
                <div className="mt-6 p-4 border-2 border-dashed border-gray-300 rounded-lg">
                  <div className="text-center">
                    <FaUpload className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                    <p className="text-gray-600">Upload new document</p>
                    <button className="mt-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                      Choose File
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

const UnifiedCustomerManagement = () => {
  return (
    <AdminLayout>
      <UnifiedCustomerManagementContent />
    </AdminLayout>
  );
};

export default UnifiedCustomerManagement;