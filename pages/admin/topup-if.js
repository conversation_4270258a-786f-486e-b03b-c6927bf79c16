import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { FiPhone, FiDollarSign, FiClock, FiCheckCircle, FiXCircle, FiArrowLeft, FiSearch, FiFilter } from 'react-icons/fi';

const TopUpIF = () => {
  const router = useRouter();
  const { store } = router.query;
  const [topupData, setTopupData] = useState([]);
  const [ifProducts, setIfProducts] = useState([]);
  const [customers, setCustomers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [productsLoading, setProductsLoading] = useState(true);
  const [customersLoading, setCustomersLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [newTopup, setNewTopup] = useState({
    phoneNumber: '',
    selectedProduct: '',
    selectedCustomer: '',
    note: ''
  });

  // Fetch IF products from inventory
  useEffect(() => {
    const fetchIFProducts = async () => {
      try {
        setProductsLoading(true);
        const response = await fetch('/api/inventory');

        if (!response.ok) {
          throw new Error('Failed to fetch inventory');
        }

        const inventory = await response.json();

        // Filter for IF products only
        const ifProductsFiltered = inventory.filter(item =>
          item.sku &&
          item.sku.includes('taiwan.prepaid.if') &&
          item.activestatus === "1" &&
          item.price > 0
        );

        console.log('Found IF products:', ifProductsFiltered.length);
        setIfProducts(ifProductsFiltered);
      } catch (error) {
        console.error('Error fetching IF products:', error);
        setIfProducts([]);
      } finally {
        setProductsLoading(false);
      }
    };

    fetchIFProducts();
  }, []);

  // Fetch customers
  useEffect(() => {
    const fetchCustomers = async () => {
      try {
        setCustomersLoading(true);
        const response = await fetch('/api/customers');

        if (!response.ok) {
          throw new Error('Failed to fetch customers');
        }

        const data = await response.json();
        const customersData = data.customers || data;

        // Format customers for display
        const formattedCustomers = customersData.map(customer => ({
          id: customer.id,
          name: customer.personalDetails?.name || customer.name || `${customer.personalDetails?.firstName || ''} ${customer.personalDetails?.lastName || ''}`.trim(),
          email: customer.contactInfo?.email || customer.email || '',
          phone: customer.contactInfo?.primaryPhone || customer.phone || ''
        }));

        setCustomers(formattedCustomers);
      } catch (error) {
        console.error('Error fetching customers:', error);
        setCustomers([]);
      } finally {
        setCustomersLoading(false);
      }
    };

    fetchCustomers();
  }, []);

  useEffect(() => {
    // Giả lập dữ liệu top up IF với sản phẩm thực
    const mockData = [
      {
        id: 1,
        phoneNumber: '0901234567',
        productSku: 'taiwan.prepaid.if.WPXn',
        productName: 'IF150 NT(NẠP TỰ ĐỘNG VÀO SỐ ĐIỆN THOẠI)',
        amount: 146,
        currency: 'NT',
        status: 'completed',
        createdAt: '2024-01-15 10:30:00',
        completedAt: '2024-01-15 10:32:15',
        note: 'Nạp thẻ cho cuộc gọi/nhắn tin'
      },
      {
        id: 2,
        phoneNumber: '0987654321',
        productSku: 'taiwan.prepaid.if.VKVh',
        productName: 'IF 350 NT(NẠP TỰ ĐỘNG VÀO SỐ ĐIỆN THOẠI)',
        amount: 284,
        currency: 'NT',
        status: 'pending',
        createdAt: '2024-01-15 11:15:00',
        completedAt: null,
        note: 'Nạp thẻ cho cuộc gọi/nhắn tin'
      },
      {
        id: 3,
        phoneNumber: '0912345678',
        productSku: 'taiwan.prepaid.if.GXqW',
        productName: 'IF mạng 30 ngày 599(NẠP TỰ ĐỘNG VÀO SỐ ĐIỆN THOẠI)',
        amount: 570,
        currency: 'NT',
        status: 'failed',
        createdAt: '2024-01-15 09:45:00',
        completedAt: null,
        note: 'Nạp thẻ MẠNG 4G cho số điện thoại trả trước'
      }
    ];
    
    setTimeout(() => {
      setTopupData(mockData);
      setLoading(false);
    }, 1000);
  }, []);

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Find the selected product
    const selectedProduct = ifProducts.find(product => product.sku === newTopup.selectedProduct);

    if (!selectedProduct) {
      alert('Vui lòng chọn sản phẩm IF');
      return;
    }

    // Find the selected customer if any
    const selectedCustomer = newTopup.selectedCustomer ?
      customers.find(customer => customer.id === newTopup.selectedCustomer) : null;

    try {
      // Create the topup order/transaction
      const orderData = {
        customerId: selectedCustomer?.id || null,
        customerName: selectedCustomer?.name || null,
        phoneNumber: newTopup.phoneNumber,
        items: [{
          sku: selectedProduct.sku,
          name: selectedProduct.name,
          price: selectedProduct.price,
          currency: selectedProduct.currency,
          quantity: 1,
          type: 'topup'
        }],
        totalAmount: selectedProduct.price,
        currency: selectedProduct.currency,
        paymentStatus: 'pending',
        status: 'pending',
        note: newTopup.note,
        source: 'admin_topup_if'
      };

      // Here you would typically send this to your API
      // const response = await fetch('/api/admin/orders', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(orderData)
      // });

      // For now, just add to local state
      const newRecord = {
        id: topupData.length + 1,
        phoneNumber: newTopup.phoneNumber,
        productSku: selectedProduct.sku,
        productName: selectedProduct.name,
        amount: selectedProduct.price,
        currency: selectedProduct.currency,
        status: 'pending',
        createdAt: new Date().toLocaleString('vi-VN'),
        completedAt: null,
        note: newTopup.note,
        customerId: selectedCustomer?.id || null,
        customerName: selectedCustomer?.name || 'Khách lẻ'
      };

      setTopupData([newRecord, ...topupData]);
      setNewTopup({ phoneNumber: '', selectedProduct: '', selectedCustomer: '', note: '' });

      if (selectedCustomer) {
        alert(`Đã tạo nạp thẻ IF cho khách hàng: ${selectedCustomer.name}`);
      } else {
        alert('Đã tạo nạp thẻ IF (khách lẻ)');
      }
    } catch (error) {
      console.error('Error creating topup:', error);
      alert('Lỗi khi tạo nạp thẻ');
    }
  };

  const filteredData = topupData.filter(item => {
    const matchesSearch = (item.phoneNumber || '').includes(searchTerm) ||
                         (item.customerName || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (item.note || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (item.productName || '').toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFilter = filterStatus === 'all' || item.status === filterStatus;
    return matchesSearch && matchesFilter;
  });

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed': return 'text-green-600 bg-green-100';
      case 'pending': return 'text-yellow-600 bg-yellow-100';
      case 'failed': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'completed': return 'Hoàn thành';
      case 'pending': return 'Đang xử lý';
      case 'failed': return 'Thất bại';
      default: return 'Không xác định';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'completed': return <FiCheckCircle className="w-4 h-4" />;
      case 'pending': return <FiClock className="w-4 h-4" />;
      case 'failed': return <FiXCircle className="w-4 h-4" />;
      default: return null;
    }
  };

  // Format currency display
  const formatCurrency = (amount, currency) => {
    if (!currency) {
      return `${amount} (KHÔNG RÕ LOẠI TIỀN)`;
    }

    switch (currency) {
      case 'VND':
        return `${amount.toLocaleString('vi-VN')}đ`;
      case 'NT':
      case 'NT$':
      case 'NTD':
      case 'TWD':
        return `NT$ ${amount.toLocaleString()}`;
      case 'USD':
      case '$':
        return `$${amount.toFixed(2)}`;
      default:
        return `${currency} ${amount}`;
    }
  };

  // Calculate revenue by currency
  const revenueStats = topupData
    .filter(item => item.status === 'completed')
    .reduce((acc, item) => {
      const currency = item.currency || 'UNKNOWN';
      if (!acc[currency]) {
        acc[currency] = { total: 0, count: 0 };
      }
      acc[currency].total += item.amount || 0;
      acc[currency].count += 1;
      return acc;
    }, {});

  // Format revenue display
  const formatRevenueDisplay = (revenueByCurrency) => {
    if (!revenueByCurrency || Object.keys(revenueByCurrency).length === 0) {
      return <span className="text-gray-500">Chưa có doanh thu</span>;
    }

    return Object.entries(revenueByCurrency).map(([currency, stats]) => (
      <div key={currency} className="mb-1">
        {formatCurrency(stats.total, currency)}
      </div>
    ));
  };

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <button
            onClick={() => router.push(`/admin`)}
            className="flex items-center text-blue-600 hover:text-blue-800 mb-4 transition-colors"
          >
            <FiArrowLeft className="w-5 h-5 mr-2" />
            Quay lại trang chủ Admin
          </button>
          
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 flex items-center">
                <FiPhone className="w-8 h-8 mr-3 text-blue-600" />
                Top Up IF Number
              </h1>
              <p className="text-gray-600 mt-2">Quản lý nạp tiền số điện thoại IF</p>
            </div>
            
            <div className="text-right">
              <div className="text-sm text-gray-500">Tổng giao dịch hôm nay</div>
              <div className="text-2xl font-bold text-blue-600">
                {topupData.filter(item => item.createdAt.includes('2024-01-15')).length}
              </div>
            </div>
          </div>
        </div>

        {/* Thống kê nhanh */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Hoàn thành</p>
                <p className="text-2xl font-bold text-green-600">
                  {topupData.filter(item => item.status === 'completed').length}
                </p>
              </div>
              <FiCheckCircle className="w-8 h-8 text-green-600" />
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Đang xử lý</p>
                <p className="text-2xl font-bold text-yellow-600">
                  {topupData.filter(item => item.status === 'pending').length}
                </p>
              </div>
              <FiClock className="w-8 h-8 text-yellow-600" />
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Thất bại</p>
                <p className="text-2xl font-bold text-red-600">
                  {topupData.filter(item => item.status === 'failed').length}
                </p>
              </div>
              <FiXCircle className="w-8 h-8 text-red-600" />
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Tổng doanh thu</p>
                <div className="text-lg font-bold text-blue-600">
                  {formatRevenueDisplay(revenueStats)}
                </div>
              </div>
              <FiDollarSign className="w-8 h-8 text-blue-600" />
            </div>
          </div>
        </div>

        {/* Thống kê sản phẩm IF */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Sản phẩm IF có sẵn</p>
                <p className="text-2xl font-bold text-blue-600">{ifProducts.length}</p>
              </div>
              <FiPhone className="w-8 h-8 text-blue-600" />
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Tổng giao dịch</p>
                <p className="text-2xl font-bold text-green-600">{topupData.length}</p>
              </div>
              <FiDollarSign className="w-8 h-8 text-green-600" />
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Thành công hôm nay</p>
                <p className="text-2xl font-bold text-purple-600">
                  {topupData.filter(item =>
                    item.status === 'completed' &&
                    item.createdAt.includes('2024-01-15')
                  ).length}
                </p>
              </div>
              <FiCheckCircle className="w-8 h-8 text-purple-600" />
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Form tạo top up mới */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200">
              <div className="p-6 border-b border-gray-200">
                <h2 className="text-xl font-semibold text-gray-900">Tạo Top Up IF Mới</h2>
              </div>
              
              <form onSubmit={handleSubmit} className="p-6 space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Khách hàng (tùy chọn)
                  </label>
                  {customersLoading ? (
                    <div className="text-center py-2">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mx-auto"></div>
                      <p className="text-xs text-gray-500 mt-1">Đang tải khách hàng...</p>
                    </div>
                  ) : (
                    <select
                      value={newTopup.selectedCustomer}
                      onChange={(e) => setNewTopup({...newTopup, selectedCustomer: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="">Chọn khách hàng (hoặc để trống cho khách lẻ)</option>
                      {customers.map((customer) => (
                        <option key={customer.id} value={customer.id}>
                          {customer.name} - {customer.phone} - {customer.email}
                        </option>
                      ))}
                    </select>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Số điện thoại (tùy chọn)
                  </label>
                  <input
                    type="tel"
                    value={newTopup.phoneNumber}
                    onChange={(e) => setNewTopup({...newTopup, phoneNumber: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Nhập số điện thoại..."
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Sản phẩm IF
                  </label>
                  {productsLoading ? (
                    <div className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50">
                      Đang tải sản phẩm IF...
                    </div>
                  ) : (
                    <select
                      value={newTopup.selectedProduct}
                      onChange={(e) => setNewTopup({...newTopup, selectedProduct: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    >
                      <option value="">Chọn sản phẩm IF</option>
                      {ifProducts.map((product) => (
                        <option key={product.sku} value={product.sku}>
                          {product.name} - {product.currency} {product.price}
                        </option>
                      ))}
                    </select>
                  )}
                  {ifProducts.length === 0 && !productsLoading && (
                    <p className="text-sm text-red-600 mt-1">Không tìm thấy sản phẩm IF nào</p>
                  )}
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Ghi chú
                  </label>
                  <textarea
                    value={newTopup.note}
                    onChange={(e) => setNewTopup({...newTopup, note: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    rows="3"
                    placeholder="Ghi chú thêm..."
                  />
                </div>
                
                <button
                  type="submit"
                  className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors font-medium"
                >
                  Tạo Top Up IF
                </button>
              </form>
            </div>
          </div>

          {/* Danh sách top up */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200">
              <div className="p-6 border-b border-gray-200">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                  <h2 className="text-xl font-semibold text-gray-900">Lịch sử Top Up IF</h2>
                  
                  <div className="flex flex-col sm:flex-row gap-3">
                    <div className="relative">
                      <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                      <input
                        type="text"
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="Tìm kiếm..."
                      />
                    </div>
                    
                    <div className="relative">
                      <FiFilter className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                      <select
                        value={filterStatus}
                        onChange={(e) => setFilterStatus(e.target.value)}
                        className="pl-10 pr-8 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 appearance-none"
                      >
                        <option value="all">Tất cả</option>
                        <option value="completed">Hoàn thành</option>
                        <option value="pending">Đang xử lý</option>
                        <option value="failed">Thất bại</option>
                      </select>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="overflow-x-auto">
                {loading ? (
                  <div className="p-8 text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                    <p className="text-gray-500 mt-2">Đang tải dữ liệu...</p>
                  </div>
                ) : filteredData.length === 0 ? (
                  <div className="p-8 text-center text-gray-500">
                    <FiPhone className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                    <p>Không có dữ liệu top up nào</p>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-1 xl:grid-cols-2 gap-4 p-4">
                    {filteredData.map((item) => (
                      <div key={item.id} className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                        <div className="flex items-center mb-3">
                          <FiPhone className="w-4 h-4 text-gray-400 mr-2" />
                          <span className="text-sm font-medium text-gray-900">
                            {item.customerName || 'Khách lẻ'}
                            {item.phoneNumber && ` - ${item.phoneNumber}`}
                          </span>
                        </div>

                        <div className="mb-3">
                          <p className="text-xs text-gray-500 mb-1">Sản phẩm IF</p>
                          <p className="text-sm font-medium text-gray-900">
                            {item.productName || 'Không xác định'}
                          </p>
                          {item.productSku && (
                            <p className="text-xs text-gray-500">SKU: {item.productSku}</p>
                          )}
                        </div>

                        <div className="grid grid-cols-2 gap-3 mb-3">
                          <div>
                            <p className="text-xs text-gray-500 mb-1">Giá</p>
                            <span className="text-sm font-semibold text-gray-900">
                              {formatCurrency(item.amount, item.currency)}
                            </span>
                          </div>
                          <div>
                            <p className="text-xs text-gray-500 mb-1">Trạng thái</p>
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(item.status)}`}>
                              {getStatusIcon(item.status)}
                              <span className="ml-1">{getStatusText(item.status)}</span>
                            </span>
                          </div>
                        </div>
                        
                        <div className="mb-3">
                          <p className="text-xs text-gray-500 mb-1">Thời gian tạo</p>
                          <p className="text-sm text-gray-700">{item.createdAt}</p>
                        </div>
                        
                        {item.note && (
                          <div>
                            <p className="text-xs text-gray-500 mb-1">Ghi chú</p>
                            <p className="text-sm text-gray-700">{item.note}</p>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TopUpIF;