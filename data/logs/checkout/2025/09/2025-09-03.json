[{"timestamp": "2025-09-03T02:35:03.907Z", "event": "CHECKOUT_PROCESS", "data": {"process": "general", "data": {"message": "Initializing Checkout", "data": {"storeId": "magshop"}}}}, {"timestamp": "2025-09-03T02:35:03.911Z", "event": "CHECKOUT_PROCESS", "data": {"process": "general", "data": {"message": "Router query params", "data": {"store": "magshop"}}}}, {"timestamp": "2025-09-03T02:35:03.911Z", "event": "CHECKOUT_PROCESS", "data": {"process": "general", "data": {"message": "Available affiliate IDs", "data": []}}}, {"timestamp": "2025-09-03T02:35:03.912Z", "event": "CHECKOUT_PROCESS", "data": {"process": "general", "data": {"message": "Allowed payment methods", "data": ["cod", "7-11", "7-11-card", "family-mart", "ok-mart", "taiwanbanktransfer", "banktransfer", "vietnambanktransfer"]}}}, {"timestamp": "2025-09-03T02:35:03.913Z", "event": "CHECKOUT_PROCESS", "data": {"process": "general", "data": {"message": "Calculated store total for currency", "data": {"storeId": "magshop", "currency": "VND", "total": 0, "itemCount": 0}}}}, {"timestamp": "2025-09-03T02:35:03.913Z", "event": "CHECKOUT_PROCESS", "data": {"process": "general", "data": {"message": "<PERSON><PERSON><PERSON><PERSON> phần thanh toán đã chọn", "data": {"method": "cod", "componentExists": true, "availableMethods": ["cod", "7-11", "family-mart", "ok-mart"]}}}}, {"timestamp": "2025-09-03T02:36:34.194Z", "event": "CHECKOUT_PROCESS", "data": {"process": "general", "data": {"message": "Initializing Checkout", "data": {"storeId": "magshop"}}}}, {"timestamp": "2025-09-03T02:36:34.198Z", "event": "CHECKOUT_PROCESS", "data": {"process": "general", "data": {"message": "Router query params", "data": {"store": "magshop"}}}}, {"timestamp": "2025-09-03T02:36:34.198Z", "event": "CHECKOUT_PROCESS", "data": {"process": "general", "data": {"message": "Available affiliate IDs", "data": []}}}, {"timestamp": "2025-09-03T02:36:34.198Z", "event": "CHECKOUT_PROCESS", "data": {"process": "general", "data": {"message": "Allowed payment methods", "data": ["cod", "7-11", "7-11-card", "family-mart", "ok-mart", "taiwanbanktransfer", "banktransfer", "vietnambanktransfer"]}}}, {"timestamp": "2025-09-03T02:36:34.198Z", "event": "CHECKOUT_PROCESS", "data": {"process": "general", "data": {"message": "Calculated store total for currency", "data": {"storeId": "magshop", "currency": "VND", "total": 0, "itemCount": 0}}}}, {"timestamp": "2025-09-03T02:36:34.198Z", "event": "CHECKOUT_PROCESS", "data": {"process": "general", "data": {"message": "<PERSON><PERSON><PERSON><PERSON> phần thanh toán đã chọn", "data": {"method": "cod", "componentExists": true, "availableMethods": ["cod", "7-11", "family-mart", "ok-mart"]}}}}, {"timestamp": "2025-09-03T03:43:33.190Z", "event": "CHECKOUT_PROCESS", "data": {"process": "general", "data": {"message": "Initializing Checkout", "data": {"storeId": "magshop"}}}}, {"timestamp": "2025-09-03T03:43:33.191Z", "event": "CHECKOUT_PROCESS", "data": {"process": "general", "data": {"message": "Router query params", "data": {"store": "magshop"}}}}, {"timestamp": "2025-09-03T03:43:33.191Z", "event": "CHECKOUT_PROCESS", "data": {"process": "general", "data": {"message": "Available affiliate IDs", "data": []}}}, {"timestamp": "2025-09-03T03:43:33.192Z", "event": "CHECKOUT_PROCESS", "data": {"process": "general", "data": {"message": "Allowed payment methods", "data": ["cod", "7-11", "7-11-card", "family-mart", "ok-mart", "taiwanbanktransfer", "banktransfer", "vietnambanktransfer"]}}}, {"timestamp": "2025-09-03T03:43:33.192Z", "event": "CHECKOUT_PROCESS", "data": {"process": "general", "data": {"message": "Calculated store total for currency", "data": {"storeId": "magshop", "currency": "VND", "total": 0, "itemCount": 0}}}}, {"timestamp": "2025-09-03T03:43:33.192Z", "event": "CHECKOUT_PROCESS", "data": {"process": "general", "data": {"message": "<PERSON><PERSON><PERSON><PERSON> phần thanh toán đã chọn", "data": {"method": "cod", "componentExists": true, "availableMethods": ["cod", "7-11", "family-mart", "ok-mart"]}}}}, {"timestamp": "2025-09-03T03:46:12.437Z", "event": "CHECKOUT_PROCESS", "data": {"process": "general", "data": {"message": "Initializing Checkout", "data": {"storeId": "magshop"}}}}, {"timestamp": "2025-09-03T03:46:12.442Z", "event": "CHECKOUT_PROCESS", "data": {"process": "general", "data": {"message": "Router query params", "data": {"store": "magshop"}}}}, {"timestamp": "2025-09-03T03:46:12.442Z", "event": "CHECKOUT_PROCESS", "data": {"process": "general", "data": {"message": "Available affiliate IDs", "data": []}}}, {"timestamp": "2025-09-03T03:46:12.444Z", "event": "CHECKOUT_PROCESS", "data": {"process": "general", "data": {"message": "Allowed payment methods", "data": ["cod", "7-11", "7-11-card", "family-mart", "ok-mart", "taiwanbanktransfer", "banktransfer", "vietnambanktransfer"]}}}, {"timestamp": "2025-09-03T03:46:12.444Z", "event": "CHECKOUT_PROCESS", "data": {"process": "general", "data": {"message": "Calculated store total for currency", "data": {"storeId": "magshop", "currency": "VND", "total": 0, "itemCount": 0}}}}, {"timestamp": "2025-09-03T03:46:12.444Z", "event": "CHECKOUT_PROCESS", "data": {"process": "general", "data": {"message": "<PERSON><PERSON><PERSON><PERSON> phần thanh toán đã chọn", "data": {"method": "cod", "componentExists": true, "availableMethods": ["cod", "7-11", "family-mart", "ok-mart"]}}}}, {"timestamp": "2025-09-03T04:17:58.991Z", "event": "CHECKOUT_PROCESS", "data": {"process": "general", "data": {"message": "Initializing Checkout", "data": {"storeId": "magshop"}}}}, {"timestamp": "2025-09-03T04:17:59.005Z", "event": "CHECKOUT_PROCESS", "data": {"process": "general", "data": {"message": "Router query params", "data": {"store": "magshop"}}}}, {"timestamp": "2025-09-03T04:17:59.005Z", "event": "CHECKOUT_PROCESS", "data": {"process": "general", "data": {"message": "Available affiliate IDs", "data": []}}}, {"timestamp": "2025-09-03T04:17:59.007Z", "event": "CHECKOUT_PROCESS", "data": {"process": "general", "data": {"message": "Allowed payment methods", "data": ["cod", "7-11", "7-11-card", "family-mart", "ok-mart", "taiwanbanktransfer", "banktransfer", "vietnambanktransfer"]}}}, {"timestamp": "2025-09-03T04:17:59.007Z", "event": "CHECKOUT_PROCESS", "data": {"process": "general", "data": {"message": "Calculated store total for currency", "data": {"storeId": "magshop", "currency": "VND", "total": 0, "itemCount": 0}}}}, {"timestamp": "2025-09-03T04:17:59.008Z", "event": "CHECKOUT_PROCESS", "data": {"process": "general", "data": {"message": "<PERSON><PERSON><PERSON><PERSON> phần thanh toán đã chọn", "data": {"method": "cod", "componentExists": true, "availableMethods": ["cod", "7-11", "family-mart", "ok-mart"]}}}}]