<!DOCTYPE html>
<html>
<head>
    <title>Test Authentication Setup</title>
</head>
<body>
    <h1>Setting up test authentication...</h1>
    <script>
        // Set customer data in localStorage
        const customerData = {
            id: 'test_customer_123',
            name: 'Test Customer',
            email: '<EMAIL>',
            phone: '123456789',
            occupation: 'Engineer',
            employer: 'Tech Corp',
            nationality: 'Vietnam'
        };
        
        // Set for both store-specific and general keys
        localStorage.setItem('magshop_customerData', JSON.stringify(customerData));
        localStorage.setItem('customerData', JSON.stringify(customerData));
        
        // Set customer ID for API calls
        localStorage.setItem('SHOPME_CUSTOMER_ID', 'test_customer_123');
        localStorage.setItem('SHOPME_CUSTOMER_INFO', JSON.stringify(customerData));
        localStorage.setItem('SHOPME_USER_ID', 'test_customer_123');
        
        console.log('Customer data set in localStorage:', customerData);
        
        // Redirect to dashboard after a short delay
        setTimeout(() => {
            window.location.href = '/magshop/customer/dashboard';
        }, 1000);
    </script>
    <p>Redirecting to dashboard...</p>
</body>
</html>