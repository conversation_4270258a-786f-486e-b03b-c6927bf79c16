import React from 'react';
import Documents from './Documents';

interface DocumentsSectionProps {
  isActive: boolean;
  toggleSection: () => void;
}

const DocumentsSection: React.FC<DocumentsSectionProps> = ({ isActive, toggleSection }) => {
  return (
    <div className="bg-white rounded-xl shadow-sm overflow-hidden">
      <div 
        onClick={toggleSection}
        className="p-4 border-b border-gray-100 flex justify-between items-center cursor-pointer hover:bg-gray-50 transition-colors"
      >
        <div className="flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-blue-500" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clipRule="evenodd" />
          </svg>
          <h2 className="text-xl font-medium text-gray-800">Tài liệu của tôi</h2>
        </div>
        <svg 
          xmlns="http://www.w3.org/2000/svg" 
          className={`h-5 w-5 text-gray-400 transition-transform duration-200 ${isActive ? 'transform rotate-180' : ''}`} 
          viewBox="0 0 20 20" 
          fill="currentColor"
        >
          <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
        </svg>
      </div>
      <div 
        className={`transition-all duration-300 ease-in-out ${
          isActive ? 'max-h-none opacity-100' : 'max-h-0 opacity-0'
        }`}
        style={{ 
          overflow: isActive ? 'visible' : 'hidden'
        }}
      >
        <div className="p-1 sm:p-4 md:p-6">
          <Documents />
        </div>
      </div>
    </div>
  );
};

export default DocumentsSection; 